#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UBICRC修复工具 - 基于IDA Pro伪代码分析的Python实现
支持读取config.txt配置文件进行批量CRC修复

作者: AI Assistant
版本: 1.0
"""

import os
import sys
import struct
from typing import List, Tuple

class UBICRCFixer:
    """UBICRC修复工具类，基于标准CRC32算法"""
    
    def __init__(self):
        """初始化CRC32查找表和反向查找表"""
        # 生成标准CRC32查找表（多项式0xEDB88320）
        self.crc_table = self._generate_crc_table()
        # 生成反向查找表用于高效CRC调整
        self.reverse_table = self._generate_reverse_table()
        
    def _generate_crc_table(self) -> List[int]:
        """生成CRC32查找表"""
        table = []
        for i in range(256):
            crc = i
            for _ in range(8):
                if crc & 1:
                    crc = (crc >> 1) ^ 0xEDB88320
                else:
                    crc >>= 1
            table.append(crc & 0xFFFFFFFF)
        return table
    
    def _generate_reverse_table(self) -> dict:
        """生成反向查找表"""
        reverse_table = {}
        for idx, value in enumerate(self.crc_table):
            reverse_table[value] = idx
        return reverse_table
    
    def calculate_crc32(self, data: bytes, start_crc: int = 0xFFFFFFFF) -> int:
        """
        计算CRC32值
        
        Args:
            data: 要计算的数据
            start_crc: 初始CRC值，默认0xFFFFFFFF
            
        Returns:
            计算得到的CRC32值
        """
        crc = start_crc
        for byte in data:
            index = (crc ^ byte) & 0xFF
            crc = (crc >> 8) ^ self.crc_table[index]
        return crc & 0xFFFFFFFF
    
    def find_crc_adjustment_bruteforce(self, current_crc: int, target_crc: int) -> bytes:
        """
        使用暴力搜索找到4字节调整序列（模拟原始C代码行为）

        Args:
            current_crc: 当前CRC32值
            target_crc: 目标CRC32值

        Returns:
            4字节调整序列
        """
        # 模拟原始C代码的暴力搜索算法
        v25 = current_crc & 0xFF  # 当前CRC的低8位
        v26 = current_crc >> 8    # 当前CRC的高24位

        # 尝试所有可能的4字节组合
        for v24 in range(0x100000000):  # 32位整数范围
            # 提取4个字节
            byte0 = v24 & 0xFF
            byte1 = (v24 >> 8) & 0xFF
            byte2 = (v24 >> 16) & 0xFF
            byte3 = (v24 >> 24) & 0xFF

            # 模拟C代码的CRC计算过程
            v27 = self.crc_table[v25 ^ byte0] ^ v26
            v28 = self.crc_table[(v27 & 0xFF) ^ byte1] ^ (v27 >> 8)
            v29 = self.crc_table[(v28 & 0xFF) ^ byte2] ^ (v28 >> 8)
            final_crc = self.crc_table[(v29 & 0xFF) ^ byte3] ^ (v29 >> 8)

            if final_crc == target_crc:
                # 找到匹配的调整字节，按小端序返回
                return struct.pack('<I', v24)

        # 如果暴力搜索失败，使用高效算法作为备选
        return self.find_crc_adjustment_efficient(current_crc, target_crc)

    def find_crc_adjustment_efficient(self, current_crc: int, target_crc: int) -> bytes:
        """
        使用高效反向CRC算法计算4字节调整序列

        Args:
            current_crc: 当前CRC32值
            target_crc: 目标CRC32值

        Returns:
            4字节调整序列
        """
        # 使用反向CRC算法计算调整字节
        temp = target_crc
        adjustment = bytearray(4)

        # 从后往前计算每个字节
        for i in range(3, -1, -1):
            # 计算需要的查找表值
            table_value = temp ^ (current_crc >> 8)

            # 查找对应的字节值
            if table_value in self.reverse_table:
                byte_val = self.reverse_table[table_value]
            else:
                # 备用搜索方法
                byte_val = next((idx for idx, val in enumerate(self.crc_table)
                               if val == table_value), 0)

            # 计算调整字节
            adjustment[i] = (current_crc ^ byte_val) & 0xFF

            # 更新CRC状态
            temp = current_crc
            current_crc = (current_crc >> 8) ^ self.crc_table[(current_crc ^ adjustment[i]) & 0xFF]

        return bytes(adjustment)
    
    def parse_config_file(self, config_path: str) -> List[Tuple[int, int, int]]:
        """
        解析config.txt配置文件
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            配置项列表，每项为(写入偏移, CRC起始位置, 目标CRC值)
        """
        configs = []
        
        if not os.path.exists(config_path):
            print(f"错误: 配置文件 {config_path} 不存在")
            return configs
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue
                    
                    try:
                        parts = line.split(',')
                        if len(parts) != 3:
                            print(f"警告: 第{line_num}行格式错误，跳过: {line}")
                            continue
                        
                        write_offset = int(parts[0].strip())
                        crc_start = int(parts[1].strip())
                        target_crc = int(parts[2].strip())
                        
                        configs.append((write_offset, crc_start, target_crc))
                        
                    except ValueError as e:
                        print(f"警告: 第{line_num}行数值解析错误，跳过: {line} ({e})")
                        continue
                        
        except Exception as e:
            print(f"错误: 读取配置文件失败: {e}")
            
        return configs
    
    def process_file(self, input_path: str, config_path: str, output_path: str) -> bool:
        """
        处理文件的CRC修复
        
        Args:
            input_path: 输入文件路径
            config_path: 配置文件路径
            output_path: 输出文件路径
            
        Returns:
            是否成功
        """
        # 读取输入文件
        if not os.path.exists(input_path):
            print(f"错误: 输入文件 {input_path} 不存在")
            return False
        
        try:
            with open(input_path, 'rb') as f:
                file_data = bytearray(f.read())
        except Exception as e:
            print(f"错误: 读取输入文件失败: {e}")
            return False
        
        # 解析配置文件
        configs = self.parse_config_file(config_path)
        if not configs:
            print("错误: 没有有效的配置项")
            return False
        
        print(f"读取到 {len(configs)} 个配置项")
        
        # 处理每个配置项
        success_count = 0
        for i, (write_offset, crc_start, target_crc) in enumerate(configs):
            print(f"\n处理配置项 {i+1}/{len(configs)}: 偏移={write_offset}, 起始={crc_start}, 目标CRC=0x{target_crc:08X}")
            
            # 验证偏移范围
            if write_offset < 0 or write_offset + 4 > len(file_data):
                print(f"错误: 写入偏移 {write_offset} 超出文件范围")
                continue
            
            if crc_start < 0 or crc_start >= write_offset:
                print(f"错误: CRC起始位置 {crc_start} 无效")
                continue
            
            # 计算当前CRC（从起始位置到写入偏移前）
            crc_data = file_data[crc_start:write_offset]
            current_crc = self.calculate_crc32(crc_data)
            print(f"当前CRC: 0x{current_crc:08X}")
            
            # 计算调整字节（使用高效算法，如果失败则使用暴力搜索）
            try:
                adjustment = self.find_crc_adjustment_efficient(current_crc, target_crc)
            except:
                print("高效算法失败，使用暴力搜索...")
                adjustment = self.find_crc_adjustment_bruteforce(current_crc, target_crc)
            print(f"调整字节: {adjustment.hex().upper()}")
            
            # 写入调整字节
            file_data[write_offset:write_offset+4] = adjustment
            
            # 验证修复结果
            verify_data = file_data[crc_start:write_offset+4]
            verify_crc = self.calculate_crc32(verify_data)
            
            if verify_crc == target_crc:
                print(f"✓ 修复成功: 0x{verify_crc:08X}")
                success_count += 1
            else:
                print(f"✗ 修复失败: 期望=0x{target_crc:08X}, 实际=0x{verify_crc:08X}")
        
        # 保存输出文件
        try:
            with open(output_path, 'wb') as f:
                f.write(file_data)
            print(f"\n输出文件已保存: {output_path}")
            print(f"成功修复 {success_count}/{len(configs)} 个配置项")
            return success_count == len(configs)
            
        except Exception as e:
            print(f"错误: 保存输出文件失败: {e}")
            return False

def main():
    """主函数"""
    if len(sys.argv) != 4:
        print("UBICRC修复工具 v1.0")
        print("用法: python ubicrc_fixer.py <输入文件> <配置文件> <输出文件>")
        print("示例: python ubicrc_fixer.py system.bin config.txt systemok.bin")
        print("\n配置文件格式 (每行):")
        print("写入偏移,CRC起始位置,目标CRC值")
        print("示例: 1024,0,0x12345678")
        sys.exit(1)
    
    input_file = sys.argv[1]
    config_file = sys.argv[2]
    output_file = sys.argv[3]
    
    # 创建修复器并处理文件
    fixer = UBICRCFixer()
    success = fixer.process_file(input_file, config_file, output_file)
    
    if success:
        print("\n所有配置项修复成功!")
        sys.exit(0)
    else:
        print("\n部分或全部配置项修复失败!")
        sys.exit(1)

if __name__ == "__main__":
    main()
