import os
import sys
import struct

class CRC32Fixer:
    def __init__(self):
        # Generate CRC32 table matching standard algorithm (initial 0xFFFFFFFF, no final invert)
        self.crc_table = self._generate_crc_table()
        # Precompute reverse lookup table for CRC32
        self.reverse_table = self._generate_reverse_table()

    def _generate_crc_table(self):
        table = []
        for i in range(256):
            crc = i
            for _ in range(8):
                if crc & 1:
                    crc = (crc >> 1) ^ 0xEDB88320
                else:
                    crc >>= 1
            table.append(crc & 0xFFFFFFFF)
        return table

    def _generate_reverse_table(self):
        # Create reverse mapping for CRC table values
        reverse_table = {}
        for idx, value in enumerate(self.crc_table):
            reverse_table[value] = idx
        return reverse_table

    def calculate_crc(self, data: bytes) -> int:
        """Calculate CRC32 using your specified method (initial 0xFFFFFFFF, no final invert)"""
        crc = 0xFFFFFFFF
        for byte in data:
            index = (crc ^ byte) & 0xFF
            crc = (crc >> 8) ^ self.crc_table[index]
        return crc & 0xFFFFFFFF

    def find_crc_adjustment(self, current_crc: int, target_crc: int) -> bytes:
        """Core algorithm to find 4-byte adjustment sequence"""
        # Work backwards from target CRC to find required state
        temp = target_crc
        adjustment = bytearray(4)
        
        # Reverse engineering the CRC steps
        for i in range(3, -1, -1):
            # Calculate the table index that would produce this state
            table_value = temp ^ (current_crc >> 8)
            
            # Find which byte input would produce this table value
            if table_value in self.reverse_table:
                byte_val = self.reverse_table[table_value]
            else:
                # Fallback to brute-force search if not in reverse table
                byte_val = next((idx for idx, val in enumerate(self.crc_table) 
                                if val == table_value), 0)
                
            adjustment[i] = (current_crc ^ byte_val) & 0xFF
            temp = current_crc
            current_crc = (current_crc >> 8) ^ self.crc_table[(current_crc ^ adjustment[i]) & 0xFF]
        
        return bytes(adjustment)

    def fix_file_crc(self, input_path: str, target_crc: int, output_path: str):
        """Fix file CRC and save to new file"""
        # Read input file
        with open(input_path, 'rb') as f:
            file_data = f.read()
        
        # Calculate current CRC
        current_crc = self.calculate_crc(file_data)
        print(f"当前CRC: 0x{current_crc:08X} ({current_crc})")
        
        # Find required adjustment bytes
        adjust_bytes = self.find_crc_adjustment(current_crc, target_crc)
        print(f"计算调整字节: {adjust_bytes.hex()}")
        
        # Create new file
        with open(output_path, 'wb') as f:
            f.write(file_data)
            f.write(adjust_bytes)
        
        # Verify new file
        with open(output_path, 'rb') as f:
            new_data = f.read()
        new_crc = self.calculate_crc(new_data)
        
        print(f"新文件CRC: 0x{new_crc:08X} ({new_crc})")
        print(f"目标CRC:   0x{target_crc:08X} ({target_crc})")
        
        if new_crc == target_crc:
            print("修复成功! 输出文件:", output_path)
            return True
        else:
            print("修复失败!")
            return False

if __name__ == "__main__":
    if len(sys.argv) != 4:
        print("用法: python crc_fixer.py <输入文件> <目标CRC> <输出文件>")
        print("目标CRC可以是十六进制(0x前缀)或十进制")
        print("示例: python crc_fixer.py input.bin 0x082224C7 output.bin")
        sys.exit(1)
    
    input_file = sys.argv[1]
    target_crc_str = sys.argv[2]
    output_file = sys.argv[3]
    
    # Parse target CRC
    try:
        if target_crc_str.lower().startswith("0x"):
            target_crc = int(target_crc_str[2:], 16)
        else:
            target_crc = int(target_crc_str)
    except ValueError:
        print("错误: CRC格式无效")
        sys.exit(1)
    
    # Process file
    fixer = CRC32Fixer()
    if not fixer.fix_file_crc(input_file, target_crc, output_file):
        print("尝试备用方法...")
        
        # Fallback method: multiple adjustments
        with open(input_file, 'rb') as f:
            data = f.read()
        
        current_crc = fixer.calculate_crc(data)
        adjustments = bytearray()
        
        for attempt in range(1, 6):  # Try up to 5 adjustments
            # Calculate new adjustment
            adjust_bytes = fixer.find_crc_adjustment(
                fixer.calculate_crc(data + bytes(adjustments)), 
                target_crc
            )
            adjustments.extend(adjust_bytes)
            
            # Check if we've succeeded
            new_crc = fixer.calculate_crc(data + bytes(adjustments))
            if new_crc == target_crc:
                with open(output_file, 'wb') as f:
                    f.write(data)
                    f.write(adjustments)
                print(f"备用方法成功! 追加字节: {bytes(adjustments).hex()}")
                print(f"新CRC: 0x{new_crc:08X} (匹配目标)")
                sys.exit(0)
        
        print("所有修复尝试均失败")
        sys.exit(1)
