#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UBICRC修复工具测试脚本
创建测试数据并验证修复功能

作者: AI Assistant
版本: 1.0
"""

import os
import sys
import random
import struct
from ubicrc_fixer import UBICRCFixer

def create_test_data(filename: str, size: int = 8192) -> None:
    """
    创建测试用的二进制文件
    
    Args:
        filename: 文件名
        size: 文件大小（字节）
    """
    print(f"创建测试文件: {filename} ({size} 字节)")
    
    # 生成随机数据
    random.seed(42)  # 固定种子确保可重现
    data = bytearray()
    
    for i in range(size):
        data.append(random.randint(0, 255))
    
    # 在特定位置预留空间用于调整字节
    # 偏移1024-1027: 调整字节区域1
    data[1024:1028] = b'\x00\x00\x00\x00'
    # 偏移2048-2051: 调整字节区域2  
    data[2048:2052] = b'\x00\x00\x00\x00'
    # 偏移4096-4099: 调整字节区域3
    data[4096:4100] = b'\x00\x00\x00\x00'
    
    with open(filename, 'wb') as f:
        f.write(data)
    
    print(f"✓ 测试文件创建完成")

def create_test_config(filename: str) -> None:
    """
    创建测试配置文件
    
    Args:
        filename: 配置文件名
    """
    print(f"创建测试配置文件: {filename}")
    
    config_content = """# UBICRC测试配置文件
# 格式: 写入偏移,CRC起始位置,目标CRC值

# 测试项1: 在偏移1024处写入调整字节，使从位置0开始的CRC32为0x12345678
1024,0,305419896

# 测试项2: 在偏移2048处写入调整字节，使从位置1028开始的CRC32为0xABCDEF00
2048,1028,2882400000

# 测试项3: 在偏移4096处写入调整字节，使从位置2052开始的CRC32为0x11223344
4096,2052,287454020
"""
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print(f"✓ 测试配置文件创建完成")

def verify_results(filename: str, config_filename: str) -> bool:
    """
    验证修复结果
    
    Args:
        filename: 修复后的文件名
        config_filename: 配置文件名
        
    Returns:
        验证是否通过
    """
    print(f"\n验证修复结果: {filename}")
    
    if not os.path.exists(filename):
        print(f"错误: 文件 {filename} 不存在")
        return False
    
    # 读取修复后的文件
    with open(filename, 'rb') as f:
        data = f.read()
    
    # 创建CRC计算器
    fixer = UBICRCFixer()
    
    # 解析配置文件
    configs = fixer.parse_config_file(config_filename)
    
    all_passed = True
    
    for i, (write_offset, crc_start, target_crc) in enumerate(configs):
        print(f"\n验证配置项 {i+1}: 偏移={write_offset}, 起始={crc_start}, 目标=0x{target_crc:08X}")
        
        # 计算实际CRC
        crc_data = data[crc_start:write_offset+4]
        actual_crc = fixer.calculate_crc32(crc_data)
        
        # 显示调整字节
        adjustment_bytes = data[write_offset:write_offset+4]
        print(f"调整字节: {adjustment_bytes.hex().upper()}")
        print(f"实际CRC: 0x{actual_crc:08X}")
        
        if actual_crc == target_crc:
            print(f"✓ 验证通过")
        else:
            print(f"✗ 验证失败: 期望=0x{target_crc:08X}, 实际=0x{actual_crc:08X}")
            all_passed = False
    
    return all_passed

def run_comprehensive_test() -> bool:
    """
    运行综合测试
    
    Returns:
        测试是否通过
    """
    print("=" * 60)
    print("UBICRC修复工具 - 综合测试")
    print("=" * 60)
    
    # 测试文件名
    test_input = "test_system.bin"
    test_config = "test_config.txt"
    test_output = "test_systemok.bin"
    
    try:
        # 步骤1: 创建测试数据
        print("\n步骤1: 创建测试数据")
        create_test_data(test_input, 8192)
        create_test_config(test_config)
        
        # 步骤2: 运行修复工具
        print("\n步骤2: 运行CRC修复")
        fixer = UBICRCFixer()
        success = fixer.process_file(test_input, test_config, test_output)
        
        if not success:
            print("✗ CRC修复失败")
            return False
        
        # 步骤3: 验证结果
        print("\n步骤3: 验证修复结果")
        verify_passed = verify_results(test_output, test_config)
        
        if verify_passed:
            print("\n" + "=" * 60)
            print("✓ 所有测试通过!")
            print("=" * 60)
            return True
        else:
            print("\n" + "=" * 60)
            print("✗ 部分测试失败!")
            print("=" * 60)
            return False
            
    except Exception as e:
        print(f"\n错误: 测试过程中发生异常: {e}")
        return False
    
    finally:
        # 清理测试文件
        test_files = [test_input, test_config, test_output]
        print(f"\n清理测试文件...")
        for file in test_files:
            if os.path.exists(file):
                try:
                    os.remove(file)
                    print(f"✓ 删除 {file}")
                except:
                    print(f"警告: 无法删除 {file}")

def test_crc_calculation():
    """测试CRC计算功能"""
    print("\n测试CRC计算功能:")
    
    fixer = UBICRCFixer()
    
    # 测试已知数据的CRC
    test_data = b"Hello, World!"
    crc = fixer.calculate_crc32(test_data)
    print(f"'{test_data.decode()}' 的CRC32: 0x{crc:08X}")
    
    # 测试空数据
    empty_crc = fixer.calculate_crc32(b"")
    print(f"空数据的CRC32: 0x{empty_crc:08X}")
    
    # 测试调整字节计算
    current_crc = 0x12345678
    target_crc = 0xABCDEF00
    adjustment = fixer.find_crc_adjustment(current_crc, target_crc)
    print(f"调整字节计算: {current_crc:08X} -> {target_crc:08X} = {adjustment.hex().upper()}")

def main():
    """主函数"""
    print("UBICRC修复工具测试程序")
    
    if len(sys.argv) > 1 and sys.argv[1] == "--crc-test":
        test_crc_calculation()
        return
    
    # 运行综合测试
    success = run_comprehensive_test()
    
    if success:
        print("\n测试结论: UBICRC修复工具工作正常!")
        sys.exit(0)
    else:
        print("\n测试结论: UBICRC修复工具存在问题!")
        sys.exit(1)

if __name__ == "__main__":
    main()
