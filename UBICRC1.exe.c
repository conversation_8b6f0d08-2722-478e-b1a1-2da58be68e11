/* This file was generated by the Hex-Rays decompiler version 9.0.0.241217.
   Copyright (c) 2007-2021 Hex-Rays <<EMAIL>>

   Detected compiler: Visual C++
*/

#include <windows.h>
#include <defs.h>

#include <stdarg.h>


//-------------------------------------------------------------------------
// Function declarations

void *sub_140001000();
void *sub_140001010();
int sub_140001020(char *Format, ...);
int sub_140001080(char *Buffer, char *Format, ...);
int __fastcall main(int argc, const char **argv, const char **envp);
__int64 __fastcall UserMathErrorFunction(struct _exception *a1);
void sub_140001B20();
char sub_140001B30();
void *sub_140001B60();
void *sub_140001B68();
void sub_140001B70();
void sub_140001D88();
void __fastcall sub_140001DC4();
__int64 sub_140001E00();

//-------------------------------------------------------------------------
// Data declarations

// extern void (__stdcall *InitializeSListHead)(PSLIST_HEADER ListHead);
// extern void *(__cdecl *malloc)(size_t Size);
// extern int (__cdecl *ftell)(FILE *Stream);
// extern int (__cdecl *_stdio_common_vsscanf)(unsigned __int64 Options, const char *Buffer, size_t BufferCount, const char *Format, _locale_t Locale, va_list ArgList);
// extern size_t (__cdecl *fread)(void *Buffer, size_t ElementSize, size_t ElementCount, FILE *Stream);
// extern int (__cdecl *feof)(FILE *Stream);
// extern FILE *(__cdecl *fopen)(const char *FileName, const char *Mode);
// extern size_t (__cdecl *fwrite)(const void *Buffer, size_t ElementSize, size_t ElementCount, FILE *Stream);
// extern char *(__cdecl *fgets)(char *Buffer, int MaxCount, FILE *Stream);
// extern int (__cdecl *_stdio_common_vfprintf)(unsigned __int64 Options, FILE *Stream, const char *Format, _locale_t Locale, va_list ArgList);
// extern FILE *(__cdecl *_acrt_iob_func)(unsigned int Ix);
// extern int (__cdecl *fseek)(FILE *Stream, int Offset, int Origin);
// extern int (__cdecl *fclose)(FILE *Stream);
// extern __time64_t (__cdecl *time64)(__time64_t *Time);
// extern void (__cdecl *srand)(unsigned int Seed);
// extern int (__cdecl *rand)();
void (*i[2])(void) = { NULL, NULL }; // weak
void (*qword_140003868)(void) = NULL; // weak
__int64 qword_140005050 = -1LL; // weak
int n2_0 = 1; // weak
int n2 = 2; // weak
__int64 n0x8000 = 524288LL; // weak
__int64 qword_140005068 = 33554432LL; // weak
_DWORD dword_140005080[256] =
{
  0,
  1996959894,
  -301047508,
  -1727442502,
  124634137,
  1886057615,
  -379345611,
  -1637575261,
  249268274,
  2044508324,
  -522852066,
  -1747789432,
  162941995,
  2125561021,
  -407360249,
  -1866523247,
  498536548,
  1789927666,
  -205950648,
  -2067906082,
  450548861,
  1843258603,
  -187386543,
  -2083289657,
  325883990,
  1684777152,
  -43845254,
  -1973040660,
  335633487,
  1661365465,
  -99664541,
  -1928851979,
  997073096,
  1281953886,
  -715111964,
  -1570279054,
  1006888145,
  1258607687,
  -770865667,
  -1526024853,
  901097722,
  1119000684,
  -608450090,
  -1396901568,
  853044451,
  1172266101,
  -589951537,
  -1412350631,
  651767980,
  1373503546,
  -925412992,
  -1076862698,
  565507253,
  1454621731,
  -809855591,
  -1195530993,
  671266974,
  1594198024,
  -972236366,
  -1324619484,
  795835527,
  1483230225,
  -1050600021,
  -1234817731,
  1994146192,
  31158534,
  -1731059524,
  -271249366,
  1907459465,
  112637215,
  -1614814043,
  -390540237,
  2013776290,
  251722036,
  -1777751922,
  -519137256,
  2137656763,
  141376813,
  -1855689577,
  -429695999,
  1802195444,
  476864866,
  -2056965928,
  -228458418,
  1812370925,
  453092731,
  -2113342271,
  -183516073,
  1706088902,
  314042704,
  -1950435094,
  -54949764,
  1658658271,
  366619977,
  -1932296973,
  -69972891,
  1303535960,
  984961486,
  -1547960204,
  -725929758,
  1256170817,
  1037604311,
  -1529756563,
  -740887301,
  1131014506,
  879679996,
  -1385723834,
  -631195440,
  1141124467,
  855842277,
  -1442165665,
  -586318647,
  1342533948,
  654459306,
  -1106571248,
  -921952122,
  1466479909,
  544179635,
  -1184443383,
  -832445281,
  1591671054,
  702138776,
  -1328506846,
  -942167884,
  1504918807,
  783551873,
  -1212326853,
  -1061524307,
  -306674912,
  -1698712650,
  62317068,
  1957810842,
  -355121351,
  -1647151185,
  81470997,
  1943803523,
  -480048366,
  -1805370492,
  225274430,
  2053790376,
  -468791541,
  -1828061283,
  167816743,
  2097651377,
  -267414716,
  -2029476910,
  503444072,
  1762050814,
  -144550051,
  -2140837941,
  426522225,
  1852507879,
  -19653770,
  -1982649376,
  282753626,
  1742555852,
  -105259153,
  -1900089351,
  397917763,
  1622183637,
  -690576408,
  -1580100738,
  953729732,
  1340076626,
  -776247311,
  -1497606297,
  1068828381,
  1219638859,
  -670225446,
  -1358292148,
  906185462,
  1090812512,
  -547295293,
  -1469587627,
  829329135,
  1181335161,
  -882789492,
  -1134132454,
  628085408,
  1382605366,
  -871598187,
  -1156888829,
  570562233,
  1426400815,
  -977650754,
  -1296233688,
  733239954,
  1555261956,
  -1026031705,
  -1244606671,
  752459403,
  1541320221,
  -1687895376,
  -328994266,
  1969922972,
  40735498,
  -1677130071,
  -351390145,
  1913087877,
  83908371,
  -1782625662,
  -491226604,
  2075208622,
  213261112,
  -1831694693,
  -438977011,
  2094854071,
  198958881,
  -2032938284,
  -237706686,
  1759359992,
  534414190,
  -2118248755,
  -155638181,
  1873836001,
  414664567,
  -2012718362,
  -15766928,
  1711684554,
  285281116,
  -1889165569,
  -127750551,
  1634467795,
  376229701,
  -1609899400,
  -686959890,
  1308918612,
  956543938,
  -1486412191,
  -799009033,
  1231636301,
  1047427035,
  -1362007478,
  -640263460,
  1088359270,
  936918000,
  -1447252397,
  -558129467,
  1202900863,
  817233897,
  -1111625188,
  -893730166,
  1404277552,
  615818150,
  -1160759803,
  -841546093,
  1423857449,
  601450431,
  -1285129682,
  -1000256840,
  1567103746,
  711928724,
  -1274298825,
  -1022587231,
  1510334235,
  755167117
}; // weak
union _SLIST_HEADER ListHead_; // weak
int dword_140005A50; // weak
int dword_140005A54; // weak
int dword_140005A58; // weak
_UNKNOWN unk_140005A60; // weak
_UNKNOWN unk_140005A68; // weak
_UNKNOWN unk_140005A70; // weak
_UNKNOWN unk_140005A78; // weak


//----- (0000000140001000) ----------------------------------------------------
void *sub_140001000()
{
  return &unk_140005A68;
}

//----- (0000000140001010) ----------------------------------------------------
void *sub_140001010()
{
  return &unk_140005A60;
}

//----- (0000000140001020) ----------------------------------------------------
int sub_140001020(char *Format, ...)
{
  Stream *Stream; // rbx
  unsigned __int64 *v3; // rax
  va_list va; // [rsp+58h] [rbp+10h] BYREF

  va_start(va, Format);
  Stream = _acrt_iob_func(1u);
  v3 = (unsigned __int64 *)sub_140001000();
  return _stdio_common_vfprintf(*v3, Stream, Format, 0LL, va);
}

//----- (0000000140001080) ----------------------------------------------------
int sub_140001080(char *Buffer, char *Format, ...)
{
  unsigned __int64 *v4; // rax
  va_list va; // [rsp+60h] [rbp+18h] BYREF

  va_start(va, Format);
  v4 = (unsigned __int64 *)sub_140001010();
  return _stdio_common_vsscanf(*v4, Buffer, 0xFFFFFFFFFFFFFFFFuLL, Format, 0LL, va);
}

//----- (00000001400010E0) ----------------------------------------------------
int __fastcall main(int argc, const char **argv, const char **envp)
{
  unsigned int Seed; // eax
  char *Buffer_2; // rbp
  Stream *Stream; // rax
  Stream *Stream_1; // rbx
  int ElementCount_2; // eax
  size_t ElementCount_1; // rdi
  size_t Size; // rcx
  char *Buffer_1; // rax
  Stream *Stream_2; // rbx
  int i; // r13d
  int i_1; // r15d
  char *v14; // r14
  __int64 v15; // rsi
  int v16; // ebx
  __int64 v17; // r12
  __int64 v18; // r11
  __int64 n252; // rdi
  char *v20; // rbx
  unsigned int v21; // eax
  char *v22; // r8
  unsigned __int64 v23; // rdx
  int v24; // r9d
  __int64 v25; // r10
  unsigned int v26; // eax
  unsigned int v27; // r8d
  unsigned int v28; // r8d
  unsigned int v29; // r8d
  bool v30; // zf
  Stream *Stream_3; // rbx
  int v33; // [rsp+30h] [rbp-4D8h]
  int v34; // [rsp+30h] [rbp-4D8h]
  size_t ElementCount; // [rsp+38h] [rbp-4D0h]
  _BYTE v36[4]; // [rsp+50h] [rbp-4B8h] BYREF
  char v37[124]; // [rsp+54h] [rbp-4B4h] BYREF
  char Buffer[1024]; // [rsp+D0h] [rbp-438h] BYREF

  Seed = time64(0LL);
  srand(Seed);
  ElementCount = 0LL;
  Buffer_2 = 0LL;
  Stream = fopen("system.bin", "rb");
  Stream_1 = Stream;
  if ( Stream )
  {
    fseek(Stream, 0, 2);
    ElementCount_2 = ftell(Stream_1);
    ElementCount_1 = ElementCount_2;
    if ( ElementCount_2 )
    {
      fseek(Stream_1, 0, 0);
      Size = ElementCount_1 + 16;
      if ( ElementCount_1 >= 0xFFFFFFFFFFFFFFF0uLL )
        Size = -1LL;
      Buffer_1 = (char *)malloc(Size);
      Buffer_2 = Buffer_1;
      if ( Buffer_1 )
        ElementCount = fread(Buffer_1, 1uLL, ElementCount_1, Stream_1);
    }
    fclose(Stream_1);
  }
  else
  {
    ElementCount_1 = 0LL;
  }
  *(_OWORD *)&Buffer_2[ElementCount_1] = 0LL;
  Stream_2 = fopen("config.txt", "r");
  for ( i = 0; !feof(Stream_2); ++i )
  {
    fgets(Buffer, 1024, Stream_2);
    sub_140001080(
      Buffer,
      "%i,%i,%i",
      (unsigned int)&v36[12 * i],
      (unsigned int)&v37[12 * i],
      (unsigned int)&v37[12 * i + 4]);
  }
  fclose(Stream_2);
  i_1 = 0;
  if ( i > 0 )
  {
    v14 = v37;
    do
    {
      v15 = *(unsigned int *)v14;
      v16 = *((_DWORD *)v14 + 1);
      v17 = *((unsigned int *)v14 - 1);
      v33 = v16;
      v18 = (unsigned int)(v15 - 4);
      if ( (int)v15 - 256 < (unsigned int)v18 )
      {
        n252 = 252LL;
        v20 = &Buffer_2[(unsigned int)(v15 - 256)];
        do
        {
          *v20++ = rand();
          --n252;
        }
        while ( n252 );
        v16 = v33;
        v18 = (unsigned int)(v15 - 4);
      }
      v21 = -1;
      v22 = &Buffer_2[v17];
      do
      {
        v23 = (unsigned __int8)*v22++;
        v21 = dword_140005080[(unsigned __int8)v21 ^ v23] ^ (v21 >> 8);
      }
      while ( v22 != &Buffer_2[v15 - 4] );
      v24 = 0;
      v25 = (unsigned __int8)v21;
      v34 = 0;
      v26 = v21 >> 8;
      while ( 1 )
      {
        v27 = dword_140005080[v25 ^ (unsigned __int8)v24] ^ v26;
        v28 = dword_140005080[(unsigned __int8)v27 ^ (unsigned __int64)BYTE1(v34)] ^ (v27 >> 8);
        v29 = dword_140005080[(unsigned __int8)v28 ^ (unsigned __int64)BYTE2(v34)] ^ (v28 >> 8);
        if ( (dword_140005080[(unsigned __int8)v29 ^ (unsigned __int64)HIBYTE(v34)] ^ (v29 >> 8)) == v16 )
          break;
        v30 = v24++ == -1;
        v34 = v24;
        if ( v30 )
          goto LABEL_24;
      }
      *(_DWORD *)&Buffer_2[v18] = v24;
      sub_140001020("ok %d\n", i_1);
LABEL_24:
      ++i_1;
      v14 += 12;
    }
    while ( i_1 < i );
  }
  Stream_3 = fopen("systemok.bin", "wb+");
  fwrite(Buffer_2, 1uLL, ElementCount, Stream_3);
  fclose(Stream_3);
  return 0;
}
// 140005080: using guessed type _DWORD dword_140005080[256];

//----- (0000000140001B0C) ----------------------------------------------------
__int64 __fastcall UserMathErrorFunction(struct _exception *a1)
{
  return 0LL;
}

//----- (0000000140001B20) ----------------------------------------------------
void sub_140001B20()
{
  InitializeSListHead(&ListHead_);
}
// 140005A40: using guessed type union _SLIST_HEADER ListHead_;

//----- (0000000140001B30) ----------------------------------------------------
char sub_140001B30()
{
  return 1;
}

//----- (0000000140001B60) ----------------------------------------------------
void *sub_140001B60()
{
  return &unk_140005A78;
}

//----- (0000000140001B68) ----------------------------------------------------
void *sub_140001B68()
{
  return &unk_140005A70;
}

//----- (0000000140001B70) ----------------------------------------------------
void sub_140001B70()
{
  dword_140005A50 = 0;
}
// 140005A50: using guessed type int dword_140005A50;

//----- (0000000140001D88) ----------------------------------------------------
void sub_140001D88()
{
  void (**i)(void); // rbx

  for ( i = ::i; i < ::i; ++i )
  {
    if ( *i )
      (*i)();
  }
}
// 140003858: using guessed type void (*i[2])(void);

//----- (0000000140001DC4) ----------------------------------------------------
void __fastcall sub_140001DC4()
{
  void (**i)(void); // rbx

  for ( i = &qword_140003868; i < &qword_140003868; ++i )
  {
    if ( *i )
      (*i)();
  }
}
// 140003868: using guessed type void (*qword_140003868)(void);

//----- (0000000140001E00) ----------------------------------------------------
__int64 sub_140001E00()
{
  int v5; // r10d
  int v6; // r8d
  int _RBX_1; // r9d
  int _RAX_1; // r14d
  int _RCX_1; // edi
  int n67264; // eax
  unsigned __int64 n0x20; // rax
  __int64 v17; // rcx
  int v18; // r8d
  int _RBX_2; // r9d
  int _RDX_1; // esi
  int _RDX_2; // r10d
  unsigned int _RBX_3; // r11d
  unsigned __int64 v38; // rax
  int v39; // eax
  unsigned __int64 v40; // rax
  __int64 v41; // rcx
  int v43; // [rsp+30h] [rbp+20h]

  _RAX = 0LL;
  __asm { cpuid }
  v5 = _RDX ^ 0x49656E69;
  v6 = _RCX ^ 0x6C65746E;
  _RBX_1 = _RBX;
  _RAX_1 = _RAX;
  _RAX = 1LL;
  __asm { cpuid }
  _RCX_1 = _RCX;
  if ( !(_RBX_1 ^ 0x756E6547 | v6 | v5)
    && ((qword_140005068 = -1LL, n67264 = _RAX & 0xFFF3FF0, n0x8000 = 0x8000LL, n67264 == 67264)
     || n67264 == 132704
     || n67264 == 132720
     || (n0x20 = (unsigned int)(n67264 - 198224), (unsigned int)n0x20 <= 0x20)
     && (v17 = 0x100010001LL, _bittest64(&v17, n0x20))) )
  {
    v18 = dword_140005A58 | 1;
    dword_140005A58 |= 1u;
  }
  else
  {
    v18 = dword_140005A58;
  }
  _RBX_2 = 0;
  _RDX_1 = 0;
  _RDX_2 = 0;
  _RBX_3 = 0;
  if ( _RAX_1 >= 7 )
  {
    _RAX = 7LL;
    __asm { cpuid }
    _RDX_1 = _RDX;
    _RBX_2 = _RBX;
    if ( (_RBX & 0x200) != 0 )
      dword_140005A58 = v18 | 2;
    if ( (int)_RAX >= 1 )
    {
      _RAX = 7LL;
      __asm { cpuid }
      _RDX_2 = _RDX;
    }
    _RAX = 36LL;
    if ( _RAX_1 >= 36 )
    {
      __asm { cpuid }
      _RBX_3 = _RBX;
    }
  }
  v38 = qword_140005050 & 0xFFFFFFFFFFFFFFFEuLL;
  n2_0 = 1;
  n2 = 2;
  qword_140005050 &= ~1uLL;
  if ( (_RCX_1 & 0x100000) != 0 )
  {
    v38 &= ~0x10uLL;
    n2_0 = 2;
    qword_140005050 = v38;
    n2 = 6;
  }
  if ( (_RCX_1 & 0x8000000) != 0 )
  {
    __asm { xgetbv }
    v43 = v38;
    if ( (_RCX_1 & 0x10000000) == 0 || (v38 & 6) != 6 )
    {
LABEL_32:
      if ( (_RDX_2 & 0x200000) != 0 && (*(_QWORD *)&v43 & 0x80000LL) != 0 )
        qword_140005050 &= ~0x80uLL;
      return 0LL;
    }
    v39 = n2 | 8;
    n2_0 = 3;
    n2 |= 8u;
    if ( (_RBX_2 & 0x20) != 0 )
    {
      n2_0 = 5;
      n2 = v39 | 0x20;
      v40 = qword_140005050 & 0xFFFFFFFFFFFFFFFDuLL;
      qword_140005050 &= ~2uLL;
      if ( (_RBX_2 & 0xD0030000) != 0xD0030000 )
      {
LABEL_26:
        if ( (_RDX_1 & 0x800000) != 0 )
          qword_140005050 = v40 & 0xFFFFFFFFFEFFFFFFuLL;
        if ( (_RDX_2 & 0x80000) != 0 && (v43 & 0xE0) == 0xE0 )
        {
          dword_140005A54 = _RBX_3 & 0x400FF;
          v41 = qword_140005050 & ~(HIWORD(_RBX_3) & 7 | 0x1000028LL);
          qword_140005050 = v41;
          if ( (_RBX_3 & 0x400FF) > 1 )
            qword_140005050 = v41 & 0xFFFFFFFFFFFFFFBFuLL;
        }
        goto LABEL_32;
      }
      if ( (v43 & 0xE0) == 0xE0 )
      {
        n2 |= 0x40u;
        v40 = qword_140005050 & 0xFFFFFFFFFFFFFFDBuLL;
        n2_0 = 6;
        qword_140005050 &= 0xFFFFFFFFFFFFFFDBuLL;
        goto LABEL_26;
      }
    }
    v40 = qword_140005050;
    goto LABEL_26;
  }
  return 0LL;
}
// 140005050: using guessed type __int64 qword_140005050;
// 140005058: using guessed type int n2_0;
// 14000505C: using guessed type int n2;
// 140005060: using guessed type __int64 n0x8000;
// 140005068: using guessed type __int64 qword_140005068;
// 140005A54: using guessed type int dword_140005A54;
// 140005A58: using guessed type int dword_140005A58;

// nfuncs=75 queued=14 decompiled=14 lumina nreq=0 worse=0 better=0
// ALL OK, 14 function(s) have been successfully decompiled
